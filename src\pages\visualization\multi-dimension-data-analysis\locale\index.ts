const i18n = {
  'en-US': {
    'menu.visualization': 'Data Visualization',
    'menu.visualization.multiDimensionDataAnalysis': 'Multi-D Analysis',
    'multiDAnalysis.card.title.activeContributors': 'Active Contributors',
    'multiDAnalysis.unit': 'times',
    'multiDAnalysis.card.title.officeVisitors': 'Office Visitors',
    'multiDAnalysis.card.title.downloads': 'Downloads',
    'multiDAnalysis.card.title.dataOverview': 'Overview',
    'multiDAnalysis.card.title.todayActivity':
      'Today\'s Likes and Comments Statistics',
    'multiDAnalysis.card.title.contentTheme': 'Content theme distribution',
    'multiDAnalysis.card.title.contentSource': 'Content publishing source',
    'multiDAnalysis.dataOverview.contentProduction': 'Content production',
    'multiDAnalysis.dataOverview.contentClicks': 'Content clicks',
    'multiDAnalysis.dataOverview.contextExposure': 'Content exposure',
    'multiDAnalysis.dataOverview.activeUsers': 'Active users',
    'multiDAnalysis.cardList.userRetentionTrend': 'User retention trends',
    'multiDAnalysis.cardList.userRetention': 'User retention',
    'multiDAnalysis.cardList.contentConsumptionTrend':
      'Content consumption trends',
    'multiDAnalysis.cardList.contentConsumption': 'Content consumption',
  },
  'zh-CN': {
    'menu.visualization': '数据可视化',
    'menu.visualization.multiDimensionDataAnalysis': '多维数据分析',
    'multiDAnalysis.card.title.officeVisitors': '官网访问量',
    'multiDAnalysis.card.title.downloads': '下载量',
    'multiDAnalysis.card.title.dataOverview': '数据总览',
    'multiDAnalysis.card.title.todayActivity': '今日转赞评统计',
    'multiDAnalysis.card.title.contentTheme': '内容题材分布',
    'multiDAnalysis.card.title.contentSource': '内容发布来源',
    'multiDAnalysis.dataOverview.contentProduction': '内容生产量',
    'multiDAnalysis.dataOverview.contentClicks': '内容点击量',
    'multiDAnalysis.dataOverview.contextExposure': '内容曝光量',
    'multiDAnalysis.dataOverview.activeUsers': '活跃用户数',
    'multiDAnalysis.cardList.userRetentionTrend': '用户留存趋势',
    'multiDAnalysis.cardList.userRetention': '用户留存量',
    'multiDAnalysis.cardList.contentConsumptionTrend': '内容消费趋势',
    'multiDAnalysis.cardList.contentConsumption': '内容消费量',
  },
};

export default i18n;
